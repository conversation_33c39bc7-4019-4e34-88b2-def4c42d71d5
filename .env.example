# Database Configuration
DATABASE_URL=mysql://taskuser:taskpassword@localhost:3306/ai_task_management
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# JIRA Configuration
JIRA_BASE_URL=https://your-company.atlassian.net
JIRA_USERNAME=<EMAIL>
JIRA_API_TOKEN=your-jira-api-token

# Gmail Configuration
GMAIL_CLIENT_ID=your-gmail-oauth-client-id
GMAIL_CLIENT_SECRET=your-gmail-oauth-client-secret
GMAIL_REDIRECT_URI=http://localhost:8000/auth/gmail/callback

# WhatsApp Configuration (Twilio)
WHATSAPP_API_TOKEN=your-twilio-whatsapp-token
WHATSAPP_ACCOUNT_SID=your-twilio-account-sid
WHATSAPP_AUTH_TOKEN=your-twilio-auth-token
WHATSAPP_FROM_NUMBER=whatsapp:+***********

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Application Configuration
APP_NAME=AI Task Management System
APP_VERSION=1.0.0
APP_ENV=development
DEBUG=true

# API Configuration
API_PORT=3000
BACKEND_PORT=8000
FRONTEND_PORT=3001

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3001,http://localhost:3000

# Session Configuration
SESSION_SECRET=your-session-secret-key
SESSION_TIMEOUT=3600000

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=uploads/

# Notification Configuration
NOTIFICATION_BATCH_SIZE=50
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_DELAY=300

# AI Configuration
AI_CONFIDENCE_THRESHOLD=0.7
AI_MAX_DAILY_TASKS=20
AI_DEFAULT_TASK_DURATION=30

# Timezone Configuration
DEFAULT_TIMEZONE=UTC
SUPPORTED_TIMEZONES=UTC,America/New_York,America/Los_Angeles,Europe/London,Asia/Tokyo

# Security Configuration
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8
ACCOUNT_LOCKOUT_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=1800

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# Development Configuration
HOT_RELOAD=true
AUTO_RESTART=true
MOCK_EXTERNAL_APIS=false
