/**
 * AI Task Management System - API Server
 * Express.js server with authentication, rate limiting, and API routes
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const cookieParser = require('cookie-parser');
require('dotenv').config();

// Import middleware and routes
const authMiddleware = require('./middleware/auth');
const errorHandler = require('./middleware/errorHandler');
const logger = require('./services/logger');

// Import route handlers
const authRoutes = require('./routes/auth');
const taskRoutes = require('./routes/tasks');
const userRoutes = require('./routes/users');
const planRoutes = require('./routes/plans');
const notificationRoutes = require('./routes/notifications');

// Create Express application
const app = express();
const PORT = process.env.API_PORT || 3000;

// Trust proxy for rate limiting
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));

// CORS configuration
const corsOptions = {
    origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3001'],
    credentials: true,
    optionsSuccessStatus: 200,
};
app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    message: {
        error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Compression middleware
app.use(compression());

// Logging middleware
app.use(morgan('combined', {
    stream: {
        write: (message) => logger.info(message.trim())
    }
}));

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0'
    });
});

// API status endpoint
app.get('/api/v1/status', (req, res) => {
    res.json({
        api_version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        features: {
            authentication: true,
            task_management: true,
            ai_integration: true,
            notifications: true
        },
        endpoints: {
            auth: '/api/v1/auth',
            tasks: '/api/v1/tasks',
            users: '/api/v1/users',
            plans: '/api/v1/plans',
            notifications: '/api/v1/notifications'
        }
    });
});

// API routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/tasks', authMiddleware, taskRoutes);
app.use('/api/v1/users', authMiddleware, userRoutes);
app.use('/api/v1/plans', authMiddleware, planRoutes);
app.use('/api/v1/notifications', authMiddleware, notificationRoutes);

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'AI Task Management System API',
        version: '1.0.0',
        documentation: '/api/v1/status',
        health: '/health'
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Endpoint not found',
        message: `Cannot ${req.method} ${req.originalUrl}`,
        availableEndpoints: [
            'GET /',
            'GET /health',
            'GET /api/v1/status',
            'POST /api/v1/auth/login',
            'POST /api/v1/auth/register',
            'GET /api/v1/tasks',
            'POST /api/v1/tasks',
            'GET /api/v1/plans',
            'GET /api/v1/notifications'
        ]
    });
});

// Error handling middleware
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    process.exit(0);
});

process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    process.exit(0);
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    logger.info(`AI Task Management API server running on port ${PORT}`);
    logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.info(`Health check: http://localhost:${PORT}/health`);
    logger.info(`API status: http://localhost:${PORT}/api/v1/status`);
});

module.exports = app;
