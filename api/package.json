{"name": "ai-task-management-api", "version": "1.0.0", "description": "REST API for AI-powered task management system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mysql2": "^3.6.5", "sequelize": "^6.35.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "multer": "^1.4.5-lts.1", "axios": "^1.6.2", "node-cron": "^3.0.3", "socket.io": "^4.7.4", "redis": "^4.6.11", "winston": "^3.11.0", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["task-management", "ai", "jira", "gmail", "api", "nodejs", "express"], "author": "Your Name", "license": "MIT"}