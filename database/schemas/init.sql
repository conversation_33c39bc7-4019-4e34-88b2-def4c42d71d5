-- AI Task Management System Database Schema
-- MySQL 8.0+ Compatible

CREATE DATABASE IF NOT EXISTS ai_task_management;
USE ai_task_management;

-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    whatsapp_number VA<PERSON>HA<PERSON>(20),
    jira_username VA<PERSON><PERSON><PERSON>(255),
    jira_api_token TEXT,
    gmail_refresh_token TEXT,
    timezone VARCHAR(50) DEFAULT 'UTC',
    preferences JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email)
);

-- JIRA tasks table
CREATE TABLE jira_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    jira_id VARCHAR(50) NOT NULL,
    jira_key VARCHAR(50) NOT NULL,
    summary TEXT NOT NULL,
    description TEXT,
    priority ENUM('Highest', 'High', 'Medium', 'Low', 'Lowest') DEFAULT 'Medium',
    status VARCHAR(50) NOT NULL,
    assignee VARCHAR(255),
    reporter VARCHAR(255),
    due_date DATETIME,
    created_date DATETIME,
    updated_date DATETIME,
    project_key VARCHAR(50),
    issue_type VARCHAR(50),
    labels JSON,
    components JSON,
    story_points INT,
    sync_status ENUM('synced', 'pending', 'error') DEFAULT 'pending',
    last_synced_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_jira (user_id, jira_id),
    INDEX idx_user_status (user_id, status),
    INDEX idx_due_date (due_date),
    INDEX idx_priority (priority),
    INDEX idx_sync_status (sync_status)
);

-- Gmail tasks table
CREATE TABLE gmail_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    gmail_message_id VARCHAR(255) NOT NULL,
    thread_id VARCHAR(255),
    subject TEXT NOT NULL,
    sender_email VARCHAR(255) NOT NULL,
    sender_name VARCHAR(255),
    content TEXT,
    extracted_action TEXT,
    extracted_due_date DATETIME,
    priority ENUM('High', 'Medium', 'Low') DEFAULT 'Medium',
    received_at DATETIME NOT NULL,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_actionable BOOLEAN DEFAULT FALSE,
    action_keywords JSON,
    confidence_score DECIMAL(3,2),
    status ENUM('pending', 'processed', 'ignored') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_message (user_id, gmail_message_id),
    INDEX idx_user_actionable (user_id, is_actionable),
    INDEX idx_received_at (received_at),
    INDEX idx_status (status)
);

-- Manual tasks table
CREATE TABLE manual_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    priority ENUM('High', 'Medium', 'Low') DEFAULT 'Medium',
    due_date DATETIME,
    estimated_duration INT, -- in minutes
    category VARCHAR(100),
    tags JSON,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    created_by INT NOT NULL,
    assigned_to INT,
    parent_task_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    FOREIGN KEY (parent_task_id) REFERENCES manual_tasks(id) ON DELETE CASCADE,
    INDEX idx_user_status (user_id, status),
    INDEX idx_due_date (due_date),
    INDEX idx_priority (priority),
    INDEX idx_assigned_to (assigned_to)
);

-- Daily plans table
CREATE TABLE daily_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    plan_date DATE NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    plan_data JSON NOT NULL, -- AI-generated plan structure
    total_tasks INT DEFAULT 0,
    estimated_duration INT DEFAULT 0, -- total minutes
    priority_distribution JSON, -- High/Medium/Low counts
    ai_confidence_score DECIMAL(3,2),
    status ENUM('draft', 'active', 'completed', 'archived') DEFAULT 'draft',
    user_feedback JSON, -- User modifications and feedback
    actual_completion_rate DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date (user_id, plan_date),
    INDEX idx_plan_date (plan_date),
    INDEX idx_status (status)
);

-- Notifications table
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type ENUM('task_due', 'task_overdue', 'daily_plan', 'weekly_summary', 'custom') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    priority ENUM('high', 'medium', 'low') DEFAULT 'medium',
    channel ENUM('whatsapp', 'email', 'push', 'in_app') NOT NULL,
    scheduled_for DATETIME NOT NULL,
    sent_at DATETIME,
    status ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
    related_task_id INT,
    related_task_type ENUM('jira', 'gmail', 'manual'),
    retry_count INT DEFAULT 0,
    error_message TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_scheduled (user_id, scheduled_for),
    INDEX idx_status_channel (status, channel),
    INDEX idx_type (type)
);

-- Task resolution log table
CREATE TABLE task_resolution_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_id INT NOT NULL,
    task_type ENUM('jira', 'gmail', 'manual') NOT NULL,
    user_id INT NOT NULL,
    resolved_by INT NOT NULL,
    resolution_type ENUM('completed', 'delegated', 'cancelled', 'deferred') NOT NULL,
    remarks TEXT,
    time_spent INT, -- in minutes
    completion_rating INT CHECK (completion_rating BETWEEN 1 AND 5),
    resolved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (resolved_by) REFERENCES users(id),
    INDEX idx_task_type_id (task_type, task_id),
    INDEX idx_user_resolved (user_id, resolved_at),
    INDEX idx_resolution_type (resolution_type)
);

-- AI insights and analytics table
CREATE TABLE ai_insights (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    insight_type ENUM('productivity_pattern', 'task_completion_trend', 'priority_accuracy', 'time_estimation') NOT NULL,
    insight_data JSON NOT NULL,
    confidence_score DECIMAL(3,2),
    date_range_start DATE,
    date_range_end DATE,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_type (user_id, insight_type),
    INDEX idx_generated_at (generated_at)
);

-- Create indexes for better performance
CREATE INDEX idx_tasks_due_today ON jira_tasks (user_id, due_date) WHERE DATE(due_date) = CURDATE();
CREATE INDEX idx_gmail_actionable_today ON gmail_tasks (user_id, received_at) WHERE is_actionable = TRUE AND DATE(received_at) = CURDATE();
CREATE INDEX idx_manual_pending ON manual_tasks (user_id, status, due_date) WHERE status IN ('pending', 'in_progress');

-- Insert default admin user (optional)
INSERT INTO users (name, email, timezone) VALUES 
('System Admin', '<EMAIL>', 'UTC');
