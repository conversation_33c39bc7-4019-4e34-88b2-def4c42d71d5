# AI-Powered Task Management System

A comprehensive task management system that integrates JIRA, Gmail, and manual inputs with AI-powered prioritization and scheduling.

## Architecture Overview

### 🧠 Core Components

1. **Backend Workers (Python)** - Data collection and synchronization
2. **AI Agent (Python)** - Task analysis and plan generation  
3. **REST APIs (Node.js)** - Backend services and data management
4. **Frontend (React)** - User interface and dashboard
5. **Database (MySQL)** - Data persistence
6. **Notification Service** - WhatsApp/Push notifications

### 🔄 Data Flow

```
JIRA API ──┐
           ├──► Backend Workers ──► MySQL ──► AI Agent ──► Daily Plans
Gmail API ──┤                                    │
           │                                     ├──► REST APIs ──► React Frontend
Manual Input ──────────────────────────────────┘                    │
                                                                     │
WhatsApp/Push Notifications ◄─── Scheduler ◄─────────────────────────┘
```

## Project Structure

```
/
├── backend/                 # Python backend workers and AI agent
│   ├── workers/            # JIRA and Gmail workers
│   ├── ai_agent/           # AI task analysis and planning
│   ├── shared/             # Common utilities and models
│   └── requirements.txt    # Python dependencies
├── api/                    # Node.js REST API
│   ├── routes/             # API endpoints
│   ├── middleware/         # Authentication and validation
│   ├── services/           # Business logic
│   └── package.json        # Node.js dependencies
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API integration
│   │   └── utils/          # Utilities
│   └── package.json        # Frontend dependencies
├── database/               # Database schemas and migrations
│   ├── schemas/            # MySQL table definitions
│   └── migrations/         # Database migration scripts
├── docker/                 # Docker configuration
│   ├── docker-compose.yml  # Multi-container setup
│   └── Dockerfile.*        # Individual service containers
└── docs/                   # Documentation
    ├── api.md              # API documentation
    └── setup.md            # Setup instructions
```

## Features

### 📋 Task Sources
- **JIRA Integration**: Automatic sync of assigned tasks
- **Gmail Integration**: Email parsing for actionable items
- **Manual Input**: User-created tasks and reminders

### 🤖 AI Capabilities
- **Smart Prioritization**: AI-driven task ranking
- **Daily Planning**: Optimized schedules with time slots
- **Delegation Recommendations**: Suggest task assignments
- **Progress Tracking**: Monitor completion rates

### 📱 User Interface
- **Task Dashboard**: Unified view of all tasks
- **Daily Plan View**: AI-generated daily schedules
- **Manual Task Input**: Quick task creation
- **Real-time Updates**: Live task status updates

### 🔔 Notifications
- **Hourly Reminders**: Task due notifications
- **WhatsApp Integration**: Mobile notifications
- **Progress Reports**: Daily/weekly summaries

## Getting Started

1. **Prerequisites**
   - Python 3.9+
   - Node.js 18+
   - MySQL 8.0+
   - Docker (optional)

2. **Installation**
   ```bash
   # Clone and setup
   git clone <repository>
   cd ai-task-management
   
   # Setup backend
   cd backend && pip install -r requirements.txt
   
   # Setup API
   cd ../api && npm install
   
   # Setup frontend
   cd ../frontend && npm install
   ```

3. **Configuration**
   - Configure JIRA API credentials
   - Setup Gmail OAuth
   - Configure WhatsApp API
   - Setup MySQL database

4. **Run Services**
   ```bash
   # Using Docker
   docker-compose up
   
   # Or manually
   # Start MySQL, then:
   cd backend && python main.py
   cd api && npm start
   cd frontend && npm start
   ```

## Technology Stack

- **Backend**: Python, FastAPI/Flask, Celery
- **AI/ML**: OpenAI API, scikit-learn, pandas
- **API**: Node.js, Express.js
- **Frontend**: React, Material-UI/Tailwind CSS
- **Database**: MySQL, Redis (caching)
- **Notifications**: Twilio WhatsApp API
- **Deployment**: Docker, Docker Compose

## License

MIT License
