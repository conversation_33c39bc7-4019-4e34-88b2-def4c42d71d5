"""
AI Task Management System - Backend Main Application
FastAPI-based backend with Celery for background tasks
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import uvicorn
from loguru import logger

# Import configuration and database
from shared.config import settings
from shared.database import init_db, get_db
from shared.models import Base

# Import routers
from workers.jira_worker import router as jira_router
from workers.gmail_worker import router as gmail_router
from ai_agent.task_analyzer import router as ai_router

# Import Celery app
from shared.celery_app import celery_app


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting AI Task Management Backend...")
    
    # Initialize database
    await init_db()
    logger.info("Database initialized")
    
    # Start Celery workers (in production, run separately)
    if settings.APP_ENV == "development":
        logger.info("Development mode: Celery workers should be started separately")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AI Task Management Backend...")


# Create FastAPI application
app = FastAPI(
    title="AI Task Management System",
    description="Backend API for AI-powered task management with JIRA and Gmail integration",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*"] if settings.DEBUG else ["your-domain.com"]
)

# Include routers
app.include_router(jira_router, prefix="/api/v1/jira", tags=["JIRA"])
app.include_router(gmail_router, prefix="/api/v1/gmail", tags=["Gmail"])
app.include_router(ai_router, prefix="/api/v1/ai", tags=["AI Agent"])


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Task Management System Backend",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        db = next(get_db())
        db.execute("SELECT 1")
        
        return {
            "status": "healthy",
            "database": "connected",
            "celery": "running" if celery_app.control.inspect().active() else "stopped"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/api/v1/status")
async def api_status():
    """API status endpoint"""
    return {
        "api_version": "1.0.0",
        "environment": settings.APP_ENV,
        "debug": settings.DEBUG,
        "features": {
            "jira_integration": True,
            "gmail_integration": True,
            "ai_agent": True,
            "notifications": True
        }
    }


if __name__ == "__main__":
    # Configure logging
    logger.add(
        "logs/backend.log",
        rotation="1 day",
        retention="30 days",
        level=settings.LOG_LEVEL.upper()
    )
    
    # Run the application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.BACKEND_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
