"""
Configuration management for the AI Task Management System
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from pathlib import Path


class Settings(BaseSettings):
    """Application settings"""
    
    # Application Configuration
    APP_NAME: str = "AI Task Management System"
    APP_VERSION: str = "1.0.0"
    APP_ENV: str = "development"
    DEBUG: bool = True
    
    # Server Configuration
    BACKEND_PORT: int = 8000
    API_PORT: int = 3000
    FRONTEND_PORT: int = 3001
    
    # Database Configuration
    DATABASE_URL: str = "mysql://taskuser:taskpassword@localhost:3306/ai_task_management"
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # JWT Configuration
    JWT_SECRET: str = "your-super-secret-jwt-key-change-this-in-production"
    JWT_EXPIRES_IN: str = "7d"
    JWT_ALGORITHM: str = "HS256"
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = ""
    OPENAI_MODEL: str = "gpt-4"
    OPENAI_MAX_TOKENS: int = 2000
    OPENAI_TEMPERATURE: float = 0.7
    
    # JIRA Configuration
    JIRA_BASE_URL: str = ""
    JIRA_USERNAME: str = ""
    JIRA_API_TOKEN: str = ""
    
    # Gmail Configuration
    GMAIL_CLIENT_ID: str = ""
    GMAIL_CLIENT_SECRET: str = ""
    GMAIL_REDIRECT_URI: str = "http://localhost:8000/auth/gmail/callback"
    
    # WhatsApp Configuration
    WHATSAPP_API_TOKEN: str = ""
    WHATSAPP_ACCOUNT_SID: str = ""
    WHATSAPP_AUTH_TOKEN: str = ""
    WHATSAPP_FROM_NUMBER: str = "whatsapp:+***********"
    
    # Email Configuration
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USER: str = ""
    SMTP_PASSWORD: str = ""
    
    # Logging Configuration
    LOG_LEVEL: str = "info"
    LOG_FILE: str = "logs/app.log"
    
    # Celery Configuration
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = ["http://localhost:3001", "http://localhost:3000"]
    
    # Rate Limiting
    RATE_LIMIT_WINDOW_MS: int = 900000  # 15 minutes
    RATE_LIMIT_MAX_REQUESTS: int = 100
    
    # Session Configuration
    SESSION_SECRET: str = "your-session-secret-key"
    SESSION_TIMEOUT: int = 3600000  # 1 hour
    
    # File Upload Configuration
    MAX_FILE_SIZE: int = ********  # 10MB
    UPLOAD_PATH: str = "uploads/"
    
    # Notification Configuration
    NOTIFICATION_BATCH_SIZE: int = 50
    NOTIFICATION_RETRY_ATTEMPTS: int = 3
    NOTIFICATION_RETRY_DELAY: int = 300  # 5 minutes
    
    # AI Configuration
    AI_CONFIDENCE_THRESHOLD: float = 0.7
    AI_MAX_DAILY_TASKS: int = 20
    AI_DEFAULT_TASK_DURATION: int = 30  # minutes
    
    # Timezone Configuration
    DEFAULT_TIMEZONE: str = "UTC"
    SUPPORTED_TIMEZONES: List[str] = [
        "UTC", "America/New_York", "America/Los_Angeles", 
        "Europe/London", "Asia/Tokyo"
    ]
    
    # Security Configuration
    BCRYPT_ROUNDS: int = 12
    PASSWORD_MIN_LENGTH: int = 8
    ACCOUNT_LOCKOUT_ATTEMPTS: int = 5
    ACCOUNT_LOCKOUT_DURATION: int = 1800  # 30 minutes
    
    # Monitoring Configuration
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    HEALTH_CHECK_INTERVAL: int = 30
    
    # Development Configuration
    HOT_RELOAD: bool = True
    AUTO_RESTART: bool = True
    MOCK_EXTERNAL_APIS: bool = False
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("SUPPORTED_TIMEZONES", pre=True)
    def assemble_timezones(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("DATABASE_URL")
    def validate_database_url(cls, v):
        if not v or not v.startswith(("mysql://", "postgresql://")):
            raise ValueError("DATABASE_URL must be a valid MySQL or PostgreSQL URL")
        return v
    
    @validator("REDIS_URL")
    def validate_redis_url(cls, v):
        if not v or not v.startswith("redis://"):
            raise ValueError("REDIS_URL must be a valid Redis URL")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Create settings instance
settings = Settings()

# Ensure log directory exists
log_dir = Path(settings.LOG_FILE).parent
log_dir.mkdir(exist_ok=True)

# Ensure upload directory exists
upload_dir = Path(settings.UPLOAD_PATH)
upload_dir.mkdir(exist_ok=True)
