version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: ai-task-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: ai_task_management
      MYSQL_USER: taskuser
      MYSQL_PASSWORD: taskpassword
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ../database/schemas/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ai-task-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis for caching and task queue
  redis:
    image: redis:7-alpine
    container_name: ai-task-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-task-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Python Backend Workers
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: ai-task-backend
    environment:
      - DATABASE_URL=mysql://taskuser:taskpassword@mysql:3306/ai_task_management
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JIRA_BASE_URL=${JIRA_BASE_URL}
      - GMAIL_CLIENT_ID=${GMAIL_CLIENT_ID}
      - GMAIL_CLIENT_SECRET=${GMAIL_CLIENT_SECRET}
      - WHATSAPP_API_TOKEN=${WHATSAPP_API_TOKEN}
    volumes:
      - ../backend:/app
      - backend_logs:/app/logs
    networks:
      - ai-task-network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    restart: unless-stopped
    ports:
      - "8000:8000"

  # Celery Worker for background tasks
  celery-worker:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: ai-task-celery-worker
    command: celery -A main.celery worker --loglevel=info
    environment:
      - DATABASE_URL=mysql://taskuser:taskpassword@mysql:3306/ai_task_management
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JIRA_BASE_URL=${JIRA_BASE_URL}
      - GMAIL_CLIENT_ID=${GMAIL_CLIENT_ID}
      - GMAIL_CLIENT_SECRET=${GMAIL_CLIENT_SECRET}
      - WHATSAPP_API_TOKEN=${WHATSAPP_API_TOKEN}
    volumes:
      - ../backend:/app
      - backend_logs:/app/logs
    networks:
      - ai-task-network
    depends_on:
      - mysql
      - redis
      - backend
    restart: unless-stopped

  # Celery Beat for scheduled tasks
  celery-beat:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: ai-task-celery-beat
    command: celery -A main.celery beat --loglevel=info
    environment:
      - DATABASE_URL=mysql://taskuser:taskpassword@mysql:3306/ai_task_management
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ../backend:/app
      - backend_logs:/app/logs
    networks:
      - ai-task-network
    depends_on:
      - mysql
      - redis
      - backend
    restart: unless-stopped

  # Node.js API Server
  api:
    build:
      context: ../api
      dockerfile: ../docker/Dockerfile.api
    container_name: ai-task-api
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=mysql://taskuser:taskpassword@mysql:3306/ai_task_management
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET=${JWT_SECRET}
      - BACKEND_URL=http://backend:8000
    ports:
      - "3000:3000"
    volumes:
      - ../api:/app
      - api_logs:/app/logs
    networks:
      - ai-task-network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    restart: unless-stopped

  # React Frontend
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/Dockerfile.frontend
    container_name: ai-task-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:3000
      - REACT_APP_BACKEND_URL=http://localhost:8000
    ports:
      - "3001:3000"
    volumes:
      - ../frontend:/app
      - /app/node_modules
    networks:
      - ai-task-network
    depends_on:
      - api
    restart: unless-stopped

  # Nginx Reverse Proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: ai-task-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../docker/nginx.conf:/etc/nginx/nginx.conf
      - nginx_logs:/var/log/nginx
    networks:
      - ai-task-network
    depends_on:
      - api
      - frontend
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  backend_logs:
  api_logs:
  nginx_logs:

networks:
  ai-task-network:
    driver: bridge
