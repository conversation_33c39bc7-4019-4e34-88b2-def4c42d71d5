# AI Task Management System - Setup Guide

## Prerequisites

Before setting up the AI Task Management System, ensure you have the following installed:

- **Python 3.9+** with pip
- **Node.js 18+** with npm
- **MySQL 8.0+**
- **Redis 6.0+**
- **Docker & Docker Compose** (optional, for containerized setup)

## Quick Start with Docker

1. **<PERSON><PERSON> and navigate to the project**
   ```bash
   git clone <repository-url>
   cd ai-task-management
   ```

2. **Copy environment configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your actual API keys and configuration
   ```

3. **Start all services with Docker**
   ```bash
   cd docker
   docker-compose up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:3001
   - API: http://localhost:3000
   - Backend: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Manual Setup

### 1. Database Setup

**Install and configure MySQL:**
```bash
# Install MySQL (Ubuntu/Debian)
sudo apt update
sudo apt install mysql-server

# Start MySQL service
sudo systemctl start mysql
sudo systemctl enable mysql

# Create database and user
mysql -u root -p
```

```sql
CREATE DATABASE ai_task_management;
CREATE USER 'taskuser'@'localhost' IDENTIFIED BY 'taskpassword';
GRANT ALL PRIVILEGES ON ai_task_management.* TO 'taskuser'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

**Initialize database schema:**
```bash
mysql -u taskuser -p ai_task_management < database/schemas/init.sql
```

**Install and start Redis:**
```bash
# Ubuntu/Debian
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server

# macOS
brew install redis
brew services start redis
```

### 2. Backend Setup (Python)

```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Create logs directory
mkdir -p logs

# Run database migrations (if using Alembic)
# alembic upgrade head

# Start the backend server
python main.py
```

### 3. API Setup (Node.js)

```bash
cd api

# Install dependencies
npm install

# Create logs directory
mkdir -p logs

# Start the API server
npm run dev
```

### 4. Frontend Setup (React)

```bash
cd frontend

# Install dependencies
npm install

# Start the development server
npm start
```

### 5. Background Workers

**Start Celery workers (in separate terminals):**

```bash
cd backend
source venv/bin/activate

# Start Celery worker
celery -A main.celery worker --loglevel=info

# Start Celery beat scheduler (in another terminal)
celery -A main.celery beat --loglevel=info
```

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure the following:

#### Required API Keys

1. **OpenAI API Key**
   - Get from: https://platform.openai.com/api-keys
   - Set: `OPENAI_API_KEY=your-key-here`

2. **JIRA Configuration**
   - Base URL: `JIRA_BASE_URL=https://your-company.atlassian.net`
   - Username: `JIRA_USERNAME=<EMAIL>`
   - API Token: `JIRA_API_TOKEN=your-jira-api-token`
   - Get API token from: https://id.atlassian.com/manage-profile/security/api-tokens

3. **Gmail OAuth Setup**
   - Go to: https://console.developers.google.com/
   - Create project and enable Gmail API
   - Create OAuth 2.0 credentials
   - Set: `GMAIL_CLIENT_ID` and `GMAIL_CLIENT_SECRET`

4. **WhatsApp (Twilio) Configuration**
   - Sign up at: https://www.twilio.com/
   - Get WhatsApp sandbox or approved number
   - Set: `WHATSAPP_API_TOKEN`, `WHATSAPP_ACCOUNT_SID`, `WHATSAPP_AUTH_TOKEN`

#### Database Configuration

```env
DATABASE_URL=mysql://taskuser:taskpassword@localhost:3306/ai_task_management
REDIS_URL=redis://localhost:6379/0
```

#### Security Configuration

```env
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
SESSION_SECRET=your-session-secret-key
```

## Testing the Setup

### 1. Health Checks

```bash
# Backend health check
curl http://localhost:8000/health

# API health check
curl http://localhost:3000/health

# Frontend (should load in browser)
open http://localhost:3001
```

### 2. Database Connection

```bash
# Test MySQL connection
mysql -u taskuser -p -h localhost ai_task_management -e "SHOW TABLES;"

# Test Redis connection
redis-cli ping
```

### 3. API Endpoints

```bash
# Test backend API
curl http://localhost:8000/api/v1/status

# Test Node.js API
curl http://localhost:3000/api/v1/status
```

## Development Workflow

### 1. Making Changes

- **Backend**: Changes auto-reload with `uvicorn --reload`
- **API**: Changes auto-reload with `nodemon`
- **Frontend**: Changes auto-reload with React dev server

### 2. Running Tests

```bash
# Backend tests
cd backend
pytest

# API tests
cd api
npm test

# Frontend tests
cd frontend
npm test
```

### 3. Code Quality

```bash
# Python formatting and linting
cd backend
black .
flake8 .
mypy .

# JavaScript linting
cd api
npm run lint
npm run lint:fix
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MySQL service is running
   - Verify credentials in `.env`
   - Ensure database exists

2. **Redis Connection Failed**
   - Check Redis service is running
   - Verify Redis URL in `.env`

3. **API Keys Not Working**
   - Verify API keys are correct
   - Check API quotas and limits
   - Ensure proper permissions

4. **Port Already in Use**
   - Change ports in `.env` file
   - Kill existing processes: `lsof -ti:8000 | xargs kill -9`

### Logs

- **Backend**: `backend/logs/backend.log`
- **API**: `api/logs/api.log`
- **Frontend**: Browser console
- **Database**: MySQL error logs
- **Redis**: Redis logs

### Getting Help

1. Check the logs for error messages
2. Verify all services are running
3. Test individual components
4. Check API documentation at `/docs` endpoints

## Production Deployment

For production deployment:

1. Use Docker Compose with production configuration
2. Set up proper SSL certificates
3. Configure environment variables securely
4. Set up monitoring and logging
5. Configure backup strategies
6. Use a process manager like PM2 for Node.js
7. Set up reverse proxy with Nginx

## Next Steps

After successful setup:

1. Configure your JIRA and Gmail integrations
2. Set up WhatsApp notifications
3. Create your first manual tasks
4. Test the AI-powered daily planning
5. Customize the frontend to your needs
